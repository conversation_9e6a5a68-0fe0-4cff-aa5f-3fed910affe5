package com.smaile.health.security.util;

import com.smaile.health.security.authentication.SmaileAuthentication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;
import java.util.Set;

/**
 * Utility class for common authentication operations in the SMAILE Health platform.
 * <p>
 * This utility class provides convenient methods for working with SmaileAuthentication objects
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Slf4j
public final class AuthenticationUtils {

    private AuthenticationUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Gets the current SmaileAuthentication from the security context.
     *
     * @return Optional containing the current authentication, empty if not available
     */
    public static Optional<SmaileAuthentication> getCurrentAuthentication() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication instanceof SmaileAuthentication smaileAuth) {
                return Optional.of(smaileAuth);
            }
            return Optional.empty();
        } catch (Exception e) {
            log.debug("Error retrieving current authentication: {}", e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Gets the current authenticated user's email.
     *
     * @return Optional containing the user's email, empty if not available
     */
    public static Optional<String> getCurrentUserEmail() {
        return getCurrentAuthentication()
                .map(SmaileAuthentication::getEmail);
    }

    /**
     * Gets the current authenticated user's ID.
     *
     * @return Optional containing the user's ID, empty if not available
     */
    public static Optional<String> getCurrentUserId() {
        return getCurrentAuthentication()
                .map(SmaileAuthentication::getUserId);
    }

    /**
     * Gets the current authenticated user's name.
     *
     * @return Optional containing the user's name, empty if not available
     */
    public static Optional<String> getCurrentUserName() {
        return getCurrentAuthentication()
                .map(SmaileAuthentication::getName);
    }

    /**
     * Gets all roles for the current authenticated user.
     *
     * @return Set of user roles, empty set if not available
     */
    public static Set<String> getCurrentUserRoles() {
        return getCurrentAuthentication()
                .map(SmaileAuthentication::getAllRoles)
                .orElse(Set.of());
    }

    /**
     * Checks if the current user is authenticated.
     *
     * @return true if user is authenticated, false otherwise
     */
    public static boolean isCurrentUserAuthenticated() {
        return getCurrentAuthentication()
                .map(SmaileAuthentication::isAuthenticated)
                .orElse(false);
    }

    /**
     * Checks if the current user has a specific role.
     *
     * @param role The role to check
     * @return true if user has the role, false otherwise
     */
    public static boolean currentUserHasRole(String role) {
        if (role == null || role.trim().isEmpty()) {
            return false;
        }

        return getCurrentAuthentication()
                .map(auth -> auth.hasRole(role))
                .orElse(false);
    }

    /**
     * Checks if the current user has any of the specified roles.
     *
     * @param roles Set of roles to check
     * @return true if user has any of the roles, false otherwise
     */
    public static boolean currentUserHasAnyRole(Set<String> roles) {
        if (roles == null || roles.isEmpty()) {
            return false;
        }

        return getCurrentAuthentication()
                .map(auth -> auth.hasAnyRole(roles))
                .orElse(false);
    }

    /**
     * Checks if the current user's authentication has expired.
     *
     * @param sessionTimeoutMinutes Session timeout in minutes
     * @return true if authentication has expired, false otherwise
     */
    public static boolean isCurrentAuthenticationExpired(long sessionTimeoutMinutes) {
        return getCurrentAuthentication()
                .map(auth -> auth.isExpired(sessionTimeoutMinutes))
                .orElse(true); // Consider expired if no authentication
    }

    /**
     * Validates that an authentication object is valid and not expired.
     *
     * @param authentication        The authentication to validate
     * @param sessionTimeoutMinutes Session timeout in minutes
     * @return true if authentication is valid and not expired
     */
    public static boolean isValidAuthentication(SmaileAuthentication authentication,
                                                long sessionTimeoutMinutes) {
        if (authentication == null) {
            return false;
        }

        return authentication.isAuthenticated() && !authentication.isExpired(sessionTimeoutMinutes);
    }

    /**
     * Safely extracts the user email from an authentication object.
     *
     * @param authentication The authentication object
     * @return Optional containing the email, empty if not available
     */
    public static Optional<String> extractUserEmail(Authentication authentication) {
        if (authentication instanceof SmaileAuthentication smaileAuth) {
            return Optional.ofNullable(smaileAuth.getEmail());
        }
        return Optional.empty();
    }

    /**
     * Safely extracts the user ID from an authentication object.
     *
     * @param authentication The authentication object
     * @return Optional containing the user ID, empty if not available
     */
    public static Optional<String> extractUserId(Authentication authentication) {
        if (authentication instanceof SmaileAuthentication smaileAuth) {
            return Optional.ofNullable(smaileAuth.getUserId());
        }
        return Optional.empty();
    }

    /**
     * Creates a log-safe representation of an authentication object.
     * This method ensures no sensitive information is included in logs.
     *
     * @param authentication The authentication object
     * @return Safe string representation for logging
     */
    public static String toLogSafeString(SmaileAuthentication authentication) {
        if (authentication == null) {
            return "null";
        }

        return String.format("SmaileAuth{email='%s', authenticated=%s, roles=%d}",
                maskEmail(authentication.getEmail()),
                authentication.isAuthenticated(),
                authentication.getAllRoles().size());
    }

    /**
     * Masks an email address for safe logging.
     *
     * @param email The email to mask
     * @return Masked email address
     */
    private static String maskEmail(String email) {
        if (email == null || email.isEmpty()) {
            return "unknown";
        }

        int atIndex = email.indexOf('@');
        if (atIndex <= 0) {
            return "invalid";
        }

        String localPart = email.substring(0, atIndex);
        String domain = email.substring(atIndex);

        if (localPart.length() <= 2) {
            return "*".repeat(localPart.length()) + domain;
        }

        return localPart.charAt(0) + "*".repeat(localPart.length() - 2) +
                localPart.charAt(localPart.length() - 1) + domain;
    }

}
