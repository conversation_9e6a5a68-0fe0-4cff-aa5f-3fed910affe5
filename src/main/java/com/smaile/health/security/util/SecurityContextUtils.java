package com.smaile.health.security.util;

import com.smaile.health.security.authentication.SmaileAuthentication;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import com.smaile.health.security.context.OrganizationPermissionContext;
import com.smaile.health.security.service.AuthenticationContextService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

/**
 * Utility class for easy access to security context and permission checking.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SecurityContextUtils {

    private final AuthenticationContextService authenticationContextService;

    /**
     * Get current authenticated user
     */
    public Optional<User> getCurrentUser() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth instanceof SmaileAuthentication smaileAuthentication) {
                return Optional.ofNullable(smaileAuthentication.getActor());
            }
        } catch (Exception e) {
            log.debug("Failed to get current user", e);
        }
        return Optional.empty();
    }

    /**
     * Get current SmaileAuthentication
     */
    public Optional<SmaileAuthentication> getCurrentAuthentication() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth instanceof SmaileAuthentication smaileAuthentication) {
                return Optional.of(smaileAuthentication);
            }
        } catch (Exception e) {
            log.debug("Failed to get current authentication", e);
        }
        return Optional.empty();
    }

    /**
     * Check if current user has permission in any organization
     */
    public boolean hasPermission(String permission) {
        return getCurrentAuthentication()
            .map(auth -> authenticationContextService.hasPermissionInAnyOrganization(auth, permission))
            .orElse(false);
    }

    /**
     * Check if current user has permission in specific organization
     */
    public boolean hasPermissionInOrganization(UUID organizationId, String permission) {
        return getCurrentAuthentication()
            .map(auth -> auth.hasPermissionInOrganization(organizationId, permission))
            .orElse(false);
    }

    /**
     * Check if current user has role in specific organization
     */
    public boolean hasRoleInOrganization(UUID organizationId, String role) {
        return getCurrentAuthentication()
            .map(auth -> auth.hasRoleInOrganization(organizationId, role))
            .orElse(false);
    }

    /**
     * Get all organizations where current user has access
     */
    public Set<UUID> getAccessibleOrganizations() {
        return getCurrentAuthentication()
            .map(SmaileAuthentication::getAccessibleOrganizations)
            .orElse(Set.of());
    }

    /**
     * Get organizations where current user has specific permission
     */
    public Set<UUID> getOrganizationsWithPermission(String permission) {
        return getCurrentAuthentication()
            .map(auth -> authenticationContextService.getOrganizationsWithPermission(auth, permission))
            .orElse(Set.of());
    }

    /**
     * Get organizations where current user has specific role
     */
    public Set<UUID> getOrganizationsWithRole(String role) {
        return getCurrentAuthentication()
            .map(auth -> authenticationContextService.getOrganizationsWithRole(auth, role))
            .orElse(Set.of());
    }

    /**
     * Get permission context for specific organization
     */
    public Optional<OrganizationPermissionContext> getOrganizationContext(UUID organizationId) {
        return getCurrentAuthentication()
            .map(auth -> auth.getOrganizationContext(organizationId));
    }

    /**
     * Get all organization permission contexts for current user
     */
    public Map<UUID, OrganizationPermissionContext> getAllOrganizationContexts() {
        return getCurrentAuthentication()
            .map(SmaileAuthentication::getOrganizationPermissionContexts)
            .orElse(Map.of());
    }

    /**
     * Check if current user is super admin
     */
    public boolean isSuperAdmin() {
        return getCurrentAuthentication()
            .map(auth -> auth.getRoles() != null && 
                        auth.getRoles().stream().anyMatch(role -> role.contains("SUPER") && role.contains("ADMIN")))
            .orElse(false);
    }

    /**
     * Check if current user has administrative access in any organization
     */
    public boolean hasAdministrativeAccess() {
        return getCurrentAuthentication()
            .map(authenticationContextService::hasAdministrativeAccess)
            .orElse(false);
    }

    /**
     * Check if current user has administrative access in specific organization
     */
    public boolean hasAdministrativeAccessInOrganization(UUID organizationId) {
        return getOrganizationContext(organizationId)
            .map(context -> 
                context.getRoles().stream().anyMatch(role -> role.contains("ADMIN")) ||
                context.getPermissions().stream().anyMatch(perm -> perm.contains("manage"))
            )
            .orElse(false);
    }

    /**
     * Get current user's primary organization (from User entity)
     */
    public Optional<UUID> getPrimaryOrganizationId() {
        return getCurrentUser()
            .map(User::getOrganization)
            .map(Organization::getId);
    }

    /**
     * Check if current user can access organization (direct or inherited)
     */
    public boolean canAccessOrganization(UUID organizationId) {
        return getOrganizationContext(organizationId).isPresent();
    }

    /**
     * Get user's access summary
     */
    public Map<String, Object> getAccessSummary() {
        return getCurrentAuthentication()
            .map(authenticationContextService::getAccessSummary)
            .orElse(Map.of());
    }

    /**
     * Validate current authentication context
     */
    public boolean validateCurrentContext() {
        return getCurrentAuthentication()
            .map(authenticationContextService::validateAuthenticationContext)
            .orElse(false);
    }

    /**
     * Check if user has any of the specified permissions in organization
     */
    public boolean hasAnyPermissionInOrganization(UUID organizationId, Set<String> permissions) {
        return getOrganizationContext(organizationId)
            .map(context -> context.hasAnyPermission(permissions))
            .orElse(false);
    }

    /**
     * Check if user has all of the specified permissions in organization
     */
    public boolean hasAllPermissionsInOrganization(UUID organizationId, Set<String> permissions) {
        return getOrganizationContext(organizationId)
            .map(context -> context.hasAllPermissions(permissions))
            .orElse(false);
    }

    /**
     * Get user's permission level in organization (for UI purposes)
     */
    public String getPermissionLevel(UUID organizationId) {
        return getCurrentAuthentication()
            .map(auth -> {
                if (isSuperAdmin()) return "SUPER_ADMIN";
                
                OrganizationPermissionContext context = auth.getOrganizationContext(organizationId);
                if (context == null) return "NONE";
                
                if (context.getRoles().stream().anyMatch(role -> role.contains("ADMIN"))) {
                    return "ADMIN";
                }
                if (context.getRoles().stream().anyMatch(role -> role.contains("MANAGER"))) {
                    return "MANAGER";
                }
                if (context.getPermissions().stream().anyMatch(perm -> perm.contains("manage"))) {
                    return "MANAGE";
                }
                if (context.getPermissions().stream().anyMatch(perm -> 
                    perm.contains("create") || perm.contains("update") || perm.contains("delete"))) {
                    return "WRITE";
                }
                if (context.getPermissions().stream().anyMatch(perm -> 
                    perm.contains("read") || perm.contains("view"))) {
                    return "READ";
                }
                
                return "INHERITED";
            })
            .orElse("NONE");
    }
}
