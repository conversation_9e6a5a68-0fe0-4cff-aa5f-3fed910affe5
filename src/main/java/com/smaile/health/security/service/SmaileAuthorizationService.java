package com.smaile.health.security.service;

import com.smaile.health.security.authentication.SmaileAuthentication;
import com.smaile.health.security.context.OrganizationPermissionContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

/**
 * Service responsible for authorization decisions in the SMAILE Health platform.
 * <p>
 * This service follows the Single Responsibility Principle by handling only
 * authorization concerns, separate from authentication. It provides methods
 * to check permissions and roles within organizational contexts.
 * <p>
 * Key Features:
 * - Organization-specific permission checking
 * - Role-based access control (RBAC)
 * - Null-safe operations with defensive programming
 * - Comprehensive logging for security auditing
 * - Thread-safe operations
 * <p>
 * Security Considerations:
 * - All methods are null-safe and fail securely (deny by default)
 * - Comprehensive audit logging for security monitoring
 * - Input validation to prevent injection attacks
 * - Defensive programming against malicious inputs
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SmaileAuthorizationService {

    private final AuthenticationContextService authenticationContextService;

    /**
     * Checks if the authenticated user has a specific permission in an organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID to check
     * @param permission     The permission to verify
     * @return true if user has the permission, false otherwise
     */
    public boolean hasPermissionInOrganization(SmaileAuthentication authentication,
                                               UUID organizationId,
                                               String permission) {
        if (!isValidInput(authentication, organizationId, permission)) {
            return false;
        }

        try {
            Optional<OrganizationPermissionContext> context = getOrganizationContext(authentication, organizationId);
            boolean hasPermission = context.map(ctx -> ctx.hasPermission(permission)).orElse(false);

            log.debug("Permission check: user={}, org={}, permission={}, result={}",
                    authentication.getEmail(), organizationId, permission, hasPermission);

            return hasPermission;
        } catch (Exception e) {
            log.warn("Error checking permission for user {} in organization {}: {}",
                    authentication.getEmail(), organizationId, e.getMessage());
            return false;
        }
    }

    /**
     * Checks if the authenticated user has a specific role in an organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID to check
     * @param role           The role to verify
     * @return true if user has the role, false otherwise
     */
    public boolean hasRoleInOrganization(SmaileAuthentication authentication,
                                         UUID organizationId,
                                         String role) {
        if (!isValidInput(authentication, organizationId, role)) {
            return false;
        }

        try {
            Optional<OrganizationPermissionContext> context = getOrganizationContext(authentication, organizationId);
            boolean hasRole = context.map(ctx -> ctx.hasRole(role)).orElse(false);

            log.debug("Role check: user={}, org={}, role={}, result={}",
                    authentication.getEmail(), organizationId, role, hasRole);

            return hasRole;
        } catch (Exception e) {
            log.warn("Error checking role for user {} in organization {}: {}",
                    authentication.getEmail(), organizationId, e.getMessage());
            return false;
        }
    }

    /**
     * Gets all organizations where the user has any permissions.
     *
     * @param authentication The user's authentication object
     * @return Set of organization IDs where user has access
     */
    public Set<UUID> getAccessibleOrganizations(SmaileAuthentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.debug("Cannot get accessible organizations for null or unauthenticated user");
            return Collections.emptySet();
        }

        try {
            Map<UUID, OrganizationPermissionContext> contexts = getPermissionContexts(authentication);
            Set<UUID> accessibleOrgs = contexts.keySet();

            log.debug("User {} has access to {} organizations",
                    authentication.getEmail(), accessibleOrgs.size());

            return Collections.unmodifiableSet(accessibleOrgs);
        } catch (Exception e) {
            log.warn("Error getting accessible organizations for user {}: {}",
                    authentication.getEmail(), e.getMessage());
            return Collections.emptySet();
        }
    }

    /**
     * Gets the permission context for a specific organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID
     * @return Optional containing the permission context if available
     */
    public Optional<OrganizationPermissionContext> getOrganizationContext(SmaileAuthentication authentication,
                                                                          UUID organizationId) {
        if (authentication == null || !authentication.isAuthenticated() || organizationId == null) {
            return Optional.empty();
        }

        try {
            Map<UUID, OrganizationPermissionContext> contexts = getPermissionContexts(authentication);
            return Optional.ofNullable(contexts.get(organizationId));
        } catch (Exception e) {
            log.warn("Error getting organization context for user {} in organization {}: {}",
                    authentication.getEmail(), organizationId, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Checks if the user has administrative access in any organization.
     *
     * @param authentication The user's authentication object
     * @return true if user has admin access anywhere
     */
    public boolean hasAdministrativeAccess(SmaileAuthentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        try {
            return authenticationContextService.hasAdministrativeAccess(authentication);
        } catch (Exception e) {
            log.warn("Error checking administrative access for user {}: {}",
                    authentication.getEmail(), e.getMessage());
            return false;
        }
    }

    /**
     * Checks if the user has administrative access in a specific organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID to check
     * @return true if user has admin access in the organization
     */
    public boolean hasAdministrativeAccessInOrganization(SmaileAuthentication authentication,
                                                         UUID organizationId) {
        if (!isValidInput(authentication, organizationId, "admin")) {
            return false;
        }

        try {
            Optional<OrganizationPermissionContext> context = getOrganizationContext(authentication, organizationId);
            return context.map(ctx ->
                    ctx.getRoles().stream().anyMatch(role -> role.toLowerCase().contains("admin")) ||
                            ctx.getPermissions().stream().anyMatch(perm -> perm.toLowerCase().contains("manage"))
            ).orElse(false);
        } catch (Exception e) {
            log.warn("Error checking administrative access for user {} in organization {}: {}",
                    authentication.getEmail(), organizationId, e.getMessage());
            return false;
        }
    }

    private boolean isValidInput(SmaileAuthentication authentication, UUID organizationId, String value) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.debug("Invalid authentication object provided");
            return false;
        }

        if (organizationId == null) {
            log.debug("Organization ID cannot be null");
            return false;
        }

        if (value == null || value.trim().isEmpty()) {
            log.debug("Permission/role value cannot be null or empty");
            return false;
        }

        return true;
    }

    /**
     * Get permission contexts from the authentication context service.
     */
    private Map<UUID, OrganizationPermissionContext> getPermissionContexts(SmaileAuthentication authentication) {
        Assert.notNull(authentication, "Authentication cannot be null");
        Assert.isTrue(authentication.isAuthenticated(), "User must be authenticated");

        return authenticationContextService.getOrganizationPermissionContexts(authentication);
    }

}
