package com.smaile.health.security.service;

import com.smaile.health.domain.User;
import com.smaile.health.exception.SmaileAuthenticationException;
import com.smaile.health.security.authentication.SmaileAuthentication;
import com.smaile.health.security.authentication.SmaileAuthenticationDetails;
import com.smaile.health.security.context.OrganizationPermissionContext;
import com.smaile.health.security.context.SecurityContextBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Responsible for building and managing authentication context
 * with organization-specific permissions after successful authentication.
 * <p>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
@Slf4j
public class AuthenticationContextService {

    private final SecurityContextBuilder securityContextBuilder;

    private final Map<String, Map<UUID, OrganizationPermissionContext>> contextCache = new ConcurrentHashMap<>();

    /**
     * Build complete authentication context for a user after successful authentication.
     * Creates an immutable SmaileAuthentication with organization-specific permissions.
     *
     * @param user The authenticated user
     * @return Immutable SmaileAuthentication with complete context
     * @throws SmaileAuthenticationException if context building fails
     */
    @Transactional
    public SmaileAuthentication buildAuthenticationContext(User user) {
        Assert.notNull(user, "User cannot be null");
        Assert.notNull(user.getEmail(), "User email cannot be null");

        log.debug("Building authentication context for user: {}", user.getEmail());

        try {
            Map<UUID, OrganizationPermissionContext> permissionContexts =
                    buildOrganizationPermissionContexts(user);
            Set<String> roles = extractRolesFromContexts(permissionContexts);
            String ownerRole = user.getOrganization().get
            SmaileAuthentication authentication = SmaileAuthentication.authenticated(user, roles);
            cachePermissionContexts(user.getKeycloakId(), permissionContexts);

            log.info("Built authentication context for user {} with {} organization contexts and {} roles",
                    user.getEmail(), permissionContexts.size(), roles.size());

            return authentication;

        } catch (Exception e) {
            log.error("Failed to build authentication context for user: {}", user.getEmail(), e);
            throw new SmaileAuthenticationException("Failed to build authentication context", e);
        }
    }

    /**
     * Build organization permission contexts for a user with caching.
     *
     * @param user The user to build contexts for
     * @return Map of organization ID to permission context
     */
    public Map<UUID, OrganizationPermissionContext> buildOrganizationPermissionContexts(User user) {
        Assert.notNull(user, "User cannot be null");

        log.debug("Building organization permission contexts for user: {}", user.getEmail());
        try {
            Map<UUID, OrganizationPermissionContext> contexts =
                    securityContextBuilder.buildOrganizationPermissionContexts(user);
            log.debug("Built {} organization permission contexts for user: {}",
                    contexts.size(), user.getEmail());
            return contexts;
        } catch (Exception e) {
            log.error("Failed to build organization permission contexts for user: {}", user.getEmail(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * Get cached organization permission contexts for a user.
     *
     * @param authentication The user's authentication
     * @return Map of organization permission contexts
     */
    public Map<UUID, OrganizationPermissionContext> getOrganizationPermissionContexts(SmaileAuthentication authentication) {
        Assert.notNull(authentication, "Authentication cannot be null");
        Assert.isTrue(authentication.isAuthenticated(), "User must be authenticated");

        User user = (User) authentication.getPrincipal();
        String cacheKey = user.getKeycloakId();

        Map<UUID, OrganizationPermissionContext> cachedContexts = contextCache.get(cacheKey);
        if (cachedContexts != null) {
            log.debug("Retrieved cached permission contexts for user: {}", user.getEmail());
            return cachedContexts;
        }

        // Build fresh contexts if not cached
        log.debug("Building fresh permission contexts for user: {}", user.getEmail());
        return buildOrganizationPermissionContexts(user);
    }

    /**
     * Check if user has permission in any organization.
     *
     * @param auth       The user's authentication
     * @param permission The permission to check
     * @return true if user has the permission in any organization
     */
    public boolean hasPermissionInAnyOrganization(SmaileAuthentication auth, String permission) {
        if (auth == null || !auth.isAuthenticated() || permission == null || permission.trim().isEmpty()) {
            return false;
        }

        try {
            Map<UUID, OrganizationPermissionContext> contexts = getOrganizationPermissionContexts(auth);
            boolean hasPermission = contexts.values().stream()
                    .anyMatch(context -> context.hasPermission(permission));

            log.debug("Permission check in any organization: user={}, permission={}, result={}",
                    auth.getEmail(), permission, hasPermission);

            return hasPermission;
        } catch (Exception e) {
            log.warn("Error checking permission in any organization for user {}: {}",
                    auth.getEmail(), e.getMessage());
            return false;
        }
    }

    /**
     * Check if user has permission in specific organization or inherited from parent.
     *
     * @param auth           The user's authentication
     * @param organizationId The organization ID to check
     * @param permission     The permission to verify
     * @return true if user has the permission in the organization or inherited
     */
    public boolean hasPermissionInOrganizationHierarchy(SmaileAuthentication auth,
                                                        UUID organizationId,
                                                        String permission) {
        if (auth == null || !auth.isAuthenticated() || organizationId == null ||
                permission == null || permission.trim().isEmpty()) {
            return false;
        }

        try {
            Map<UUID, OrganizationPermissionContext> contexts = getOrganizationPermissionContexts(auth);

            // Check direct permission in the organization
            OrganizationPermissionContext directContext = contexts.get(organizationId);
            if (directContext != null && directContext.hasPermission(permission)) {
                log.debug("Direct permission found: user={}, org={}, permission={}",
                        auth.getEmail(), organizationId, permission);
                return true;
            }

            // Check inherited permissions (if implemented in future)
            // This is a placeholder for hierarchical permission inheritance
            boolean hasInheritedPermission = contexts.values().stream()
                    .filter(context -> !context.getOrganizationId().equals(organizationId))
                    .anyMatch(context -> context.hasPermission(permission));

            if (hasInheritedPermission) {
                log.debug("Inherited permission found: user={}, org={}, permission={}",
                        auth.getEmail(), organizationId, permission);
            }

            return hasInheritedPermission;

        } catch (Exception e) {
            log.warn("Error checking hierarchical permission for user {} in organization {}: {}",
                    auth.getEmail(), organizationId, e.getMessage());
            return false;
        }
    }

    /**
     * Get all organizations where user has a specific permission.
     *
     * @param auth       The user's authentication
     * @param permission The permission to search for
     * @return Set of organization IDs where user has the permission
     */
    public Set<UUID> getOrganizationsWithPermission(SmaileAuthentication auth, String permission) {
        if (auth == null || !auth.isAuthenticated() || permission == null || permission.trim().isEmpty()) {
            return Collections.emptySet();
        }

        try {
            Map<UUID, OrganizationPermissionContext> contexts = getOrganizationPermissionContexts(auth);
            Set<UUID> organizations = contexts.entrySet().stream()
                    .filter(entry -> entry.getValue().hasPermission(permission))
                    .map(Map.Entry::getKey)
                    .collect(java.util.stream.Collectors.toUnmodifiableSet());

            log.debug("Found {} organizations with permission '{}' for user: {}",
                    organizations.size(), permission, auth.getEmail());

            return organizations;
        } catch (Exception e) {
            log.warn("Error getting organizations with permission for user {}: {}",
                    auth.getEmail(), e.getMessage());
            return Collections.emptySet();
        }
    }

    /**
     * Get all organizations where user has a specific role.
     *
     * @param auth The user's authentication
     * @param role The role to search for
     * @return Set of organization IDs where user has the role
     */
    public Set<UUID> getOrganizationsWithRole(SmaileAuthentication auth, String role) {
        if (auth == null || !auth.isAuthenticated() || role == null || role.trim().isEmpty()) {
            return Collections.emptySet();
        }

        try {
            Map<UUID, OrganizationPermissionContext> contexts = getOrganizationPermissionContexts(auth);
            Set<UUID> organizations = contexts.entrySet().stream()
                    .filter(entry -> entry.getValue().hasRole(role))
                    .map(Map.Entry::getKey)
                    .collect(java.util.stream.Collectors.toUnmodifiableSet());

            log.debug("Found {} organizations with role '{}' for user: {}",
                    organizations.size(), role, auth.getEmail());

            return organizations;
        } catch (Exception e) {
            log.warn("Error getting organizations with role for user {}: {}",
                    auth.getEmail(), e.getMessage());
            return Collections.emptySet();
        }
    }

    /**
     * Check if user has administrative access in any organization.
     *
     * @param auth The user's authentication
     * @return true if user has admin access in any organization
     */
    public boolean hasAdministrativeAccess(SmaileAuthentication auth) {
        if (auth == null || !auth.isAuthenticated()) {
            return false;
        }

        try {
            Map<UUID, OrganizationPermissionContext> contexts = getOrganizationPermissionContexts(auth);

            // Check for admin roles or manage permissions
            boolean hasAdminAccess = contexts.values().stream()
                    .anyMatch(context ->
                            context.getRoles().stream().anyMatch(role -> role.toUpperCase().contains("ADMIN")) ||
                                    context.getPermissions().stream()
                                            .anyMatch(perm -> perm.toLowerCase().contains("manage"))
                    );

            log.debug("Administrative access check for user {}: {}", auth.getEmail(), hasAdminAccess);
            return hasAdminAccess;

        } catch (Exception e) {
            log.warn("Error checking administrative access for user {}: {}",
                    auth.getEmail(), e.getMessage());
            return false;
        }
    }

    /**
     * Get summary of user's access across all organizations.
     *
     * @param auth The user's authentication
     * @return Map containing access summary information
     */
    public Map<String, Object> getAccessSummary(SmaileAuthentication auth) {
        if (auth == null || !auth.isAuthenticated()) {
            return Collections.emptyMap();
        }

        try {
            Map<UUID, OrganizationPermissionContext> contexts = getOrganizationPermissionContexts(auth);
            User user = (User) auth.getPrincipal();

            int totalOrganizations = contexts.size();

            Set<String> allPermissions = contexts.values().stream()
                    .flatMap(context -> context.getPermissions().stream())
                    .collect(java.util.stream.Collectors.toUnmodifiableSet());

            Set<String> allRoles = contexts.values().stream()
                    .flatMap(context -> context.getRoles().stream())
                    .collect(java.util.stream.Collectors.toUnmodifiableSet());

            Map<String, Object> summary = Map.of(
                    "userId", user.getId(),
                    "userEmail", user.getEmail(),
                    "keycloakId", user.getKeycloakId(),
                    "totalOrganizations", totalOrganizations,
                    "totalPermissions", allPermissions.size(),
                    "totalRoles", allRoles.size(),
                    "hasAdminAccess", hasAdministrativeAccess(auth),
                    "isAuthenticated", auth.isAuthenticated()
            );

            log.debug("Generated access summary for user: {} with {} organizations",
                    user.getEmail(), totalOrganizations);

            return summary;

        } catch (Exception e) {
            log.warn("Error generating access summary for user {}: {}",
                    auth.getEmail(), e.getMessage());
            return Collections.emptyMap();
        }
    }

    /**
     * Validate authentication context integrity.
     *
     * @param auth The authentication to validate
     * @return true if authentication context is valid
     */
    public boolean validateAuthenticationContext(SmaileAuthentication auth) {
        if (auth == null) {
            log.warn("Authentication context is null");
            return false;
        }

        if (!auth.isAuthenticated()) {
            log.warn("Authentication is not authenticated");
            return false;
        }

        if (auth.getPrincipal() == null) {
            log.warn("Authentication context missing user principal");
            return false;
        }

        try {
            User user = (User) auth.getPrincipal();
            Map<UUID, OrganizationPermissionContext> contexts = getOrganizationPermissionContexts(auth);

            if (contexts.isEmpty()) {
                log.warn("User {} has no organization permission contexts", user.getEmail());
                // This might be valid for some users, so don't return false
            }

            // Validate that all contexts have valid organization IDs
            boolean hasInvalidContexts = contexts.entrySet().stream()
                    .anyMatch(entry -> !entry.getKey().equals(entry.getValue().getOrganizationId()));

            if (hasInvalidContexts) {
                log.warn("Inconsistent organization IDs in permission contexts for user: {}", user.getEmail());
                return false;
            }

            log.debug("Authentication context validation passed for user: {}", user.getEmail());
            return true;

        } catch (Exception e) {
            log.warn("Error validating authentication context: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Extract roles from organization permission contexts.
     */
    private Set<String> extractRolesFromContexts(Map<UUID, OrganizationPermissionContext> contexts) {
        return contexts.values().stream()
                .flatMap(context -> context.getRoles().stream())
                .collect(Collectors.toUnmodifiableSet());
    }

    /**
     * Cache permission contexts for a user.
     */
    private void cachePermissionContexts(String keycloakId, Map<UUID, OrganizationPermissionContext> contexts) {
        if (keycloakId != null && contexts != null) {
            contextCache.put(keycloakId, contexts);
            log.debug("Cached permission contexts for user: {}", keycloakId);
        }
    }

}
