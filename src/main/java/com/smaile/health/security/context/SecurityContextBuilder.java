package com.smaile.health.security.context;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.google.common.collect.Multimaps;
import com.smaile.health.domain.Permission;
import com.smaile.health.domain.Role;
import com.smaile.health.domain.User;
import com.smaile.health.domain.projection.UserOrganizationRolesProjection;
import com.smaile.health.repository.RoleRepository;
import com.smaile.health.repository.UserRoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service responsible for building security context with permissions for each organization
 * after successful authentication.
 */
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
@Slf4j
public class SecurityContextBuilder {

    private final UserRoleRepository userRoleRepository;
    private final RoleRepository roleRepository;

    /**
     * Build organization-specific permission contexts for a user
     */
    public Map<UUID, OrganizationPermissionContext> buildOrganizationPermissionContexts(User user) {
        log.debug("Building permission contexts for user: {}", user.getEmail());

        Map<UUID, OrganizationPermissionContext> contexts = new HashMap<>();
        Multimap<String, Permission> permissionsByRoles = getPermissionsByRoles();
        List<UserOrganizationRolesProjection> activeUserRoles = getActiveUserRoles(user);
        ArrayListMultimap<UUID, UserOrganizationRolesProjection> userRolesByOrganization = activeUserRoles.stream()
                .collect(Multimaps.toMultimap(
                        UserOrganizationRolesProjection::getOrganizationId,
                        userOrganizationRolesProjection -> userOrganizationRolesProjection,
                        ArrayListMultimap::create
                ));

        userRolesByOrganization.asMap().forEach((orgId, uor) -> {
            List<UserOrganizationRolesProjection> userOrganizationRoleProjections = uor.stream().toList();
            UUID organizationId = userOrganizationRoleProjections.get(0).getOrganizationId();
            String organizationName = userOrganizationRoleProjections.get(0).getOrganizationName();
            OrganizationPermissionContext context = buildDirectPermissionContext(organizationId, organizationName,
                    userOrganizationRoleProjections, permissionsByRoles);
            contexts.put(orgId, context);
        });

        log.debug("Built {} permission contexts for user: {}", contexts.size(), user.getEmail());
        return contexts;
    }

    private ArrayListMultimap<String, Permission> getPermissionsByRoles() {
        List<Role> roles = roleRepository.findAll();
        return roles.stream()
                .collect(ArrayListMultimap::create,
                        (map, role) -> role.getPermissions()
                                .forEach(permission -> map.put(role.getCode(), permission)),
                        Multimap::putAll
                );
    }

    /**
     * Build permission context for direct role assignments in an organization
     */
    private OrganizationPermissionContext buildDirectPermissionContext(UUID organizationId, String organizationName,
                                                                       List<UserOrganizationRolesProjection> userOrganizationRoleProjections,
                                                                       Multimap<String, Permission> userRoles) {
        Set<String> permissions = new HashSet<>();
        Set<String> roles = new HashSet<>();

        for (UserOrganizationRolesProjection userRole : userOrganizationRoleProjections) {
            String roleCode = userRole.getRoleCode();
            roles.add(roleCode);

            if (userRoles.containsKey(roleCode)) {
                Set<String> rolePermissions = userRoles.get(roleCode).stream()
                        .map(this::formatPermission)
                        .collect(Collectors.toSet());
                permissions.addAll(rolePermissions);
            }
        }

        return OrganizationPermissionContext.builder()
                .organizationId(organizationId)
                .organizationName(organizationName)
                .permissions(permissions)
                .roles(roles)
                .build();
    }

    /**
     * Get active user roles for a user
     */
    private List<UserOrganizationRolesProjection> getActiveUserRoles(User user) {
        return userRoleRepository.findByUserId(user.getId());
    }

    /**
     * Format permission as string
     */
    private String formatPermission(Permission permission) {
        StringBuilder sb = new StringBuilder();

        if (permission.getResource() != null) {
            sb.append(permission.getResource());
        }

        if (permission.getSubResource() != null) {
            sb.append(":").append(permission.getSubResource());
        }

        if (permission.getAction() != null) {
            sb.append(":").append(permission.getAction());
        }

        return sb.toString();
    }

}
