package com.smaile.health.security.authentication;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * Authentication details for SMAILE Health platform.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmaileAuthenticationDetails implements Serializable {

    @Serial
    private static final long serialVersionUID = -8480967704468832259L;

    private String keycloakId;
    private String email;
    private String preferredUsername;

    /**
     * Authentication method used
     */
    @Builder.Default
    private String authenticationMethod = "KEYCLOAK_HEADER";

    /**
     * Whether this was a successful authentication
     */
    @Builder.Default
    private Boolean successful = true;

}
