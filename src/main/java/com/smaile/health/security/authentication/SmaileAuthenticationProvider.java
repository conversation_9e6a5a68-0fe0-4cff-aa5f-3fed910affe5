package com.smaile.health.security.authentication;

import com.smaile.health.constants.Status;
import com.smaile.health.constants.UserStatus;
import com.smaile.health.domain.User;
import com.smaile.health.security.context.OrganizationPermissionContext;
import com.smaile.health.security.service.AuthenticationContextService;
import com.smaile.health.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.UUID;

/**
 * AuthenticationProvider implementation for SMAILE Health platform.
 * This provider handles authentication for users coming through Keycloak integration
 * and builds comprehensive organization-specific permission contexts.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/25
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SmaileAuthenticationProvider implements AuthenticationProvider {

    private final UserService userService;
    private final AuthenticationContextService authenticationContextService;

    /**
     * Performs authentication for SMAILE users.
     * <p>
     * This method validates the user credentials (Keycloak ID) and builds
     * a comprehensive authentication context with organization-specific permissions.
     *
     * @param authentication The authentication request containing user credentials
     * @return SmaileAuthentication with complete permission context
     * @throws AuthenticationException if authentication fails
     */
    @Override
    public SmaileAuthentication authenticate(Authentication authentication) throws AuthenticationException {
        log.debug("Starting authentication process for: {}", authentication.getName());

        try {
            SmaileAuthenticationToken token = (SmaileAuthenticationToken) authentication;
            String keycloakId = token.getKeycloakId();
            String email = token.getEmail();

            validateAuthenticationInput(keycloakId, email);
            SmaileAuthentication smaileAuthentication = authenticateUser(keycloakId);
            validateAuthenticationResult(smaileAuthentication, keycloakId);

            log.info("Authentication successful for user: {} ({})", email, keycloakId);
            return smaileAuthentication;

        } catch (AuthenticationException e) {
            log.warn("Authentication failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during authentication", e);
            throw new BadCredentialsException("Authentication failed due to system error", e);
        }
    }

    /**
     * Validates the input parameters for authentication.
     *
     * @param keycloakId The Keycloak user ID
     * @param email      The user's email address
     * @throws BadCredentialsException if validation fails
     */
    private void validateAuthenticationInput(String keycloakId, String email) {
        if (!StringUtils.hasText(keycloakId)) {
            throw new BadCredentialsException("Keycloak ID is required for authentication");
        }

        if (!StringUtils.hasText(email)) {
            log.warn("Email not provided for Keycloak ID: {}", keycloakId);
        }
    }

    /**
     * Performs the actual user authentication and context building.
     *
     * @param keycloakId The Keycloak user ID
     * @return SmaileAuthentication with complete context
     * @throws AuthenticationException if authentication fails
     */
    private SmaileAuthentication authenticateUser(String keycloakId)
            throws AuthenticationException {
        User user = findUserByKeycloakId(keycloakId);
        validateUserAccount(user);
        return authenticationContextService.buildAuthenticationContext(user);
    }

    /**
     * Finds a user by their Keycloak ID.
     *
     * @param keycloakId The Keycloak user ID
     * @return User entity
     * @throws BadCredentialsException if user not found
     */
    private User findUserByKeycloakId(String keycloakId) throws BadCredentialsException {
        try {
            User user = userService.findByKeycloakId(keycloakId);
            if (user == null) {
                throw new BadCredentialsException("User not found with Keycloak ID: " + keycloakId);
            }
            return user;
        } catch (Exception e) {
            log.error("Error finding user by Keycloak ID: {}", keycloakId, e);
            throw new BadCredentialsException("User lookup failed", e);
        }
    }

    /**
     * Validates the user account status and eligibility for authentication.
     *
     * @param user The user to validate
     * @throws DisabledException       if user account is disabled
     * @throws BadCredentialsException if user account is invalid
     */
    private void validateUserAccount(User user) throws AuthenticationException {
        if (!UserStatus.ACTIVE.equals(user.getStatus())) {
            throw new DisabledException("User account is not active: " + user.getStatus());
        }

        if (user.getUserOrganizations() == null || user.getUserOrganizations().isEmpty()) {
            throw new BadCredentialsException("User has no organization relationships");
        }

        boolean hasActiveOrganization = user.getUserOrganizations().stream()
                .anyMatch(uo -> Status.ACTIVE.equals(uo.getStatus()));

        if (!hasActiveOrganization) {
            throw new DisabledException("User has no active organization relationships");
        }
        log.debug("User account validation successful for: {}", user.getEmail());
    }

    /**
     * Validates the authentication result.
     *
     * @param smaileAuthentication The authentication result
     * @param keycloakId           The original Keycloak ID
     * @throws BadCredentialsException if validation fails
     */
    private void validateAuthenticationResult(SmaileAuthentication smaileAuthentication, String keycloakId)
            throws BadCredentialsException {

        if (smaileAuthentication == null) {
            throw new BadCredentialsException("Authentication context creation failed");
        }

        if (!smaileAuthentication.isAuthenticated()) {
            throw new BadCredentialsException("Authentication was not successful");
        }

        if (smaileAuthentication.getPrincipal() == null) {
            throw new BadCredentialsException("User context not properly established");
        }

        Map<UUID, OrganizationPermissionContext> organizationPermissionContexts =
                authenticationContextService.getOrganizationPermissionContexts(smaileAuthentication);
        if (organizationPermissionContexts == null || organizationPermissionContexts.isEmpty()) {
            log.warn("User {} has no permission contexts", keycloakId);
        }
    }

    /**
     * Determines if this provider supports the given authentication type.
     *
     * @param authentication The authentication class to check
     * @return true if this provider supports the authentication type
     */
    @Override
    public boolean supports(Class<?> authentication) {
        return SmaileAuthenticationToken.class.isAssignableFrom(authentication);
    }

}
