package com.smaile.health.domain.projection;

import java.util.UUID;

/**
 * Comprehensive projection for user context including roles and permissions
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/8/2025
 **/
public interface UserContextProjection {

    UUID getUserId();

    String getKeycloakId();

    String getUserStatus();

    UUID getRoleId();

    UUID getOrganizationId();

    String getOrganizationName();

    UUID getPermissionId();

    String getPermissionName();

}
