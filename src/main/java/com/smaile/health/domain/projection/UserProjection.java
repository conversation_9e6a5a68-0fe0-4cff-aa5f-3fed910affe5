package com.smaile.health.domain.projection;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/8/2025
 **/
public interface UserProjection {

    UUID getId();

    String getKeycloakId();

    String getStatus();

    UUID getRoleId();

    UUID getOrganizationId();

    String getOrganizationName();

    UUID getPermissionId();

    String getPermissionName();

}
