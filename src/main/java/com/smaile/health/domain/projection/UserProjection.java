package com.smaile.health.domain.projection;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/8/2025
 **/
public interface UserProjection {

    UUID getId();

    UUID getKeycloakId();

    String getEmail();

    String getStatus();

    OrganizationProjection getOwnerOrganization();

    List<OrganizationProjection> getDelegateOrganizations();

}
