package com.smaile.health.repository.impl;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.domain.projection.OrganizationProjection;
import com.smaile.health.domain.projection.PermissionProjection;
import com.smaile.health.domain.projection.RoleProjection;
import com.smaile.health.domain.projection.UserProjection;
import com.smaile.health.repository.UserRepositoryCustom;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class UserRepositoryCustomImpl implements UserRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public UserProjection findUserProjectionByKeycloakId(String keycloakId) {
        String jpql = """
            SELECT
                u.id AS id,
                u.keycloakId AS keycloakId,
                u.email AS email,
                u.status AS status,
                uo.organization.id AS organizationId,
                uo.organization.name AS organizationName,
                uo.organization.type AS organizationType,
                uo.organization.status AS organizationStatus,
                ur.role.id AS roleId,
                ur.role.name AS roleName,
                ur.role.code AS roleCode,
                p.id AS permissionId,
                p.name AS permissionName,
                p.resource AS resource,
                p.subResource AS subResource,
                p.action AS action,
                p.description AS permissionDescription,
                CASE
                    WHEN u.organization.id = uo.organization.id THEN 'owner'
                    ELSE 'delegate'
                END AS userType
            FROM User u
            INNER JOIN UserOrganization uo ON u.id = uo.user.id
            INNER JOIN UserRole ur ON ur.userOrganization.id = uo.id
            INNER JOIN ur.role.permissions p
            WHERE u.keycloakId = :keycloakId
            """;

        @SuppressWarnings("unchecked")
        List<Object[]> results = entityManager.createQuery(jpql)
                .setParameter("keycloakId", keycloakId)
                .getResultList();

        if (results.isEmpty()) {
            return null;
        }

        return mapToUserProjection(results);
    }

    private UserProjection mapToUserProjection(List<Object[]> results) {
        Map<UUID, List<Object[]>> orgGroups = results.stream()
                .collect(Collectors.groupingBy(row -> (UUID) row[4]));

        Object[] firstRow = results.get(0);
        UUID userId = (UUID) firstRow[0];
        UUID keycloakId = (UUID) firstRow[1];
        String email = (String) firstRow[2];
        String userStatus = firstRow[3].toString();

        // Build organizations with roles and permissions, separating owner vs delegate
        List<OrganizationProjection> delegateOrgs = new ArrayList<>();
        OrganizationProjection ownerOrg = null;

        for (Map.Entry<UUID, List<Object[]>> orgEntry : orgGroups.entrySet()) {
            List<Object[]> orgRows = orgEntry.getValue();
            OrganizationProjection org = buildOrganizationProjection(orgRows);
            String userType = (String) orgRows.get(0)[17];

            if ("owner".equals(userType)) {
                ownerOrg = org;
            } else {
                delegateOrgs.add(org);
            }
        }

        final OrganizationProjection finalOwnerOrg = ownerOrg;
        final List<OrganizationProjection> finalDelegateOrgs = delegateOrgs;

        return new UserProjection() {
            @Override
            public UUID getId() {
                return userId;
            }

            @Override
            public UUID getKeycloakId() {
                return keycloakId;
            }

            @Override
            public String getEmail() {
                return email;
            }

            @Override
            public String getStatus() {
                return userStatus;
            }

            @Override
            public OrganizationProjection getOwnerOrganization() {
                return finalOwnerOrg;
            }

            @Override
            public List<OrganizationProjection> getDelegateOrganizations() {
                return finalDelegateOrgs;
            }
        };
    }

    private OrganizationProjection buildOrganizationProjection(List<Object[]> orgRows) {
        Object[] firstRow = orgRows.get(0);
        UUID orgId = (UUID) firstRow[4];
        String orgName = (String) firstRow[5];
        String orgType = firstRow[6].toString();
        OrganizationStatus orgStatus = (OrganizationStatus) firstRow[7];

        Map<UUID, List<Object[]>> roleGroups = orgRows.stream()
                .collect(Collectors.groupingBy(row -> (UUID) row[8]));

        RoleProjection role = null;
        if (!roleGroups.isEmpty()) {
            Map.Entry<UUID, List<Object[]>> roleEntry = roleGroups.entrySet().iterator().next();
            role = buildRoleProjection(roleEntry.getValue());
        }

        final RoleProjection finalRole = role;

        return new OrganizationProjection() {
            @Override
            public UUID getId() {
                return orgId;
            }

            @Override
            public String getName() {
                return orgName;
            }

            @Override
            public String getType() {
                return orgType;
            }

            @Override
            public OrganizationStatus getStatus() {
                return orgStatus;
            }

            @Override
            public RoleProjection getRole() {
                return finalRole;
            }
        };
    }

    private RoleProjection buildRoleProjection(List<Object[]> roleRows) {
        Object[] firstRow = roleRows.get(0);
        String roleName = (String) firstRow[9];
        String roleCode = (String) firstRow[10];

        List<PermissionProjection> permissions = roleRows.stream()
                .map(this::buildPermissionProjection)
                .distinct()
                .collect(Collectors.toList());

        return new RoleProjection() {
            @Override
            public String getName() {
                return roleName;
            }

            @Override
            public String getCode() {
                return roleCode;
            }

            @Override
            public List<PermissionProjection> getPermissions() {
                return permissions;
            }
        };
    }

    private PermissionProjection buildPermissionProjection(Object[] row) {
        UUID permissionId = (UUID) row[11];
        String permissionName = (String) row[12];
        String resource = (String) row[13];
        String subResource = (String) row[14];
        String action = (String) row[15];
        String description = (String) row[16];

        return new PermissionProjection() {
            @Override
            public UUID getId() {
                return permissionId;
            }

            @Override
            public String getName() {
                return permissionName;
            }

            @Override
            public String getResource() {
                return resource;
            }

            @Override
            public String getSubResource() {
                return subResource;
            }

            @Override
            public String getAction() {
                return action;
            }

            @Override
            public String getDescription() {
                return description;
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) return true;
                if (!(o instanceof PermissionProjection that)) return false;
                return Objects.equals(getId(), that.getId());
            }

            @Override
            public int hashCode() {
                return Objects.hash(getId());
            }
        };
    }
}
