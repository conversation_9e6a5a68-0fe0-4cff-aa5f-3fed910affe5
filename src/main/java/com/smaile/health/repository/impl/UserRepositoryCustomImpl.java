package com.smaile.health.repository.impl;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.domain.projection.OrganizationProjection;
import com.smaile.health.domain.projection.PermissionProjection;
import com.smaile.health.domain.projection.RoleProjection;
import com.smaile.health.domain.projection.UserProjection;
import com.smaile.health.repository.UserRepositoryCustom;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class UserRepositoryCustomImpl implements UserRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public UserProjection findUserProjectionByKeycloakId(String keycloakId) {
        String sql = """
            SELECT 
                u.id as user_id,
                u.keycloak_id,
                u.email,
                u.status as user_status,
                o.id as org_id,
                o.name as org_name,
                o.type as org_type,
                o.status as org_status,
                r.id as role_id,
                r.name as role_name,
                r.code as role_code,
                p.id as permission_id,
                p.name as permission_name,
                p.resource,
                p.sub_resource,
                p.action,
                p.description as permission_description
            FROM users u
            INNER JOIN users_organizations uo ON u.id = uo.user_id
            INNER JOIN organizations o ON uo.organization_id = o.id
            INNER JOIN users_roles ur ON ur.user_organization_id = uo.id
            INNER JOIN roles r ON ur.role_id = r.id
            INNER JOIN roles_permissions rp ON r.id = rp.role_id
            INNER JOIN permissions p ON rp.permission_id = p.id
            WHERE u.keycloak_id = :keycloakId
            """;

        @SuppressWarnings("unchecked")
        List<Object[]> results = entityManager.createNativeQuery(sql)
                .setParameter("keycloakId", keycloakId)
                .getResultList();

        if (results.isEmpty()) {
            return null;
        }

        return mapToUserProjection(results);
    }

    private UserProjection mapToUserProjection(List<Object[]> results) {
        // Group results by organization
        Map<UUID, List<Object[]>> orgGroups = results.stream()
                .collect(Collectors.groupingBy(row -> UUID.fromString(row[4].toString())));

        Object[] firstRow = results.get(0);
        UUID userId = UUID.fromString(firstRow[0].toString());
        UUID keycloakId = UUID.fromString(firstRow[1].toString());
        String email = firstRow[2].toString();
        String userStatus = firstRow[3].toString();

        List<OrganizationProjection> organizations = new ArrayList<>();
        OrganizationProjection ownerOrg = null;

        for (Map.Entry<UUID, List<Object[]>> orgEntry : orgGroups.entrySet()) {
            OrganizationProjection org = buildOrganizationProjection(orgEntry.getValue());
            organizations.add(org);
            
            if (ownerOrg == null) {
                ownerOrg = org;
            }
        }

        final OrganizationProjection finalOwnerOrg = ownerOrg;
        final List<OrganizationProjection> delegateOrgs = organizations.stream()
                .filter(org -> !org.getId().equals(finalOwnerOrg.getId()))
                .collect(Collectors.toList());

        return new UserProjection() {
            @Override
            public UUID getId() {
                return userId;
            }

            @Override
            public UUID getKeycloakId() {
                return keycloakId;
            }

            @Override
            public String getEmail() {
                return email;
            }

            @Override
            public String getStatus() {
                return userStatus;
            }

            @Override
            public OrganizationProjection getOwnerOrganization() {
                return finalOwnerOrg;
            }

            @Override
            public List<OrganizationProjection> getDelegateOrganizations() {
                return delegateOrgs;
            }
        };
    }

    private OrganizationProjection buildOrganizationProjection(List<Object[]> orgRows) {
        Object[] firstRow = orgRows.get(0);
        UUID orgId = UUID.fromString(firstRow[4].toString());
        String orgName = firstRow[5].toString();
        String orgType = firstRow[6].toString();
        OrganizationStatus orgStatus = OrganizationStatus.valueOf(firstRow[7].toString());

        // Group by role
        Map<UUID, List<Object[]>> roleGroups = orgRows.stream()
                .collect(Collectors.groupingBy(row -> UUID.fromString(row[8].toString())));

        // Build role with permissions (assuming one role per organization for simplicity)
        RoleProjection role = null;
        if (!roleGroups.isEmpty()) {
            Map.Entry<UUID, List<Object[]>> roleEntry = roleGroups.entrySet().iterator().next();
            role = buildRoleProjection(roleEntry.getValue());
        }

        final RoleProjection finalRole = role;

        return new OrganizationProjection() {
            @Override
            public UUID getId() {
                return orgId;
            }

            @Override
            public String getName() {
                return orgName;
            }

            @Override
            public String getType() {
                return orgType;
            }

            @Override
            public OrganizationStatus getStatus() {
                return orgStatus;
            }

            @Override
            public RoleProjection getRole() {
                return finalRole;
            }
        };
    }

    private RoleProjection buildRoleProjection(List<Object[]> roleRows) {
        Object[] firstRow = roleRows.get(0);
        String roleName = firstRow[9].toString();
        String roleCode = firstRow[10].toString();

        // Build permissions
        List<PermissionProjection> permissions = roleRows.stream()
                .map(this::buildPermissionProjection)
                .distinct()
                .collect(Collectors.toList());

        return new RoleProjection() {
            @Override
            public String getName() {
                return roleName;
            }

            @Override
            public String getCode() {
                return roleCode;
            }

            @Override
            public List<PermissionProjection> getPermissions() {
                return permissions;
            }
        };
    }

    private PermissionProjection buildPermissionProjection(Object[] row) {
        UUID permissionId = UUID.fromString(row[11].toString());
        String permissionName = row[12].toString();
        String resource = row[13] != null ? row[13].toString() : null;
        String subResource = row[14] != null ? row[14].toString() : null;
        String action = row[15] != null ? row[15].toString() : null;
        String description = row[16] != null ? row[16].toString() : null;

        return new PermissionProjection() {
            @Override
            public UUID getId() {
                return permissionId;
            }

            @Override
            public String getName() {
                return permissionName;
            }

            @Override
            public String getResource() {
                return resource;
            }

            @Override
            public String getSubResource() {
                return subResource;
            }

            @Override
            public String getAction() {
                return action;
            }

            @Override
            public String getDescription() {
                return description;
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) return true;
                if (!(o instanceof PermissionProjection that)) return false;
                return Objects.equals(getId(), that.getId());
            }

            @Override
            public int hashCode() {
                return Objects.hash(getId());
            }
        };
    }
}
