# UserProjection Alias Mapping Guide

This document describes the alias-based mapping used in the `UserRepositoryCustomImpl` for building `UserProjection` with nested structures.

## JPQL Query Aliases

The JPQL query uses the following aliases for type-safe field access:

```sql
SELECT
    u.id AS userId,                          -- User ID
    u.keycloakId AS keycloakId,             -- User Keycloak ID
    u.email AS email,                        -- User email
    u.status AS userStatus,                  -- User status
    uo.organization.id AS organizationId,    -- Organization ID
    uo.organization.name AS organizationName, -- Organization name
    uo.organization.type AS organizationType, -- Organization type
    uo.organization.status AS organizationStatus, -- Organization status
    ur.role.id AS roleId,                    -- Role ID
    ur.role.name AS roleName,                -- Role name
    ur.role.code AS roleCode,                -- Role code
    p.id AS permissionId,                    -- Permission ID
    p.name AS permissionName,                -- Permission name
    p.resource AS resource,                  -- Permission resource
    p.subResource AS subResource,            -- Permission sub-resource
    p.action AS action,                      -- Permission action
    p.description AS permissionDescription,  -- Permission description
    CASE
        WHEN u.organization.id = uo.organization.id THEN 'owner'
        ELSE 'delegate'
    END AS userType                          -- Owner/Delegate classification
FROM User u
INNER JOIN UserOrganization uo ON u.id = uo.user.id
INNER JOIN UserRole ur ON ur.userOrganization.id = uo.id
INNER JOIN ur.role.permissions p
WHERE u.keycloakId = :keycloakId
```

## Alias Usage in Code

### User Level Mapping
```java
UUID userId = firstRow.get("userId", UUID.class);
String keycloakId = firstRow.get("keycloakId", String.class);
String email = firstRow.get("email", String.class);
String userStatus = firstRow.get("userStatus", String.class);
```

### Organization Level Mapping
```java
UUID orgId = firstRow.get("organizationId", UUID.class);
String orgName = firstRow.get("organizationName", String.class);
String orgType = firstRow.get("organizationType", String.class);
OrganizationStatus orgStatus = firstRow.get("organizationStatus", OrganizationStatus.class);
```

### Role Level Mapping
```java
String roleName = firstRow.get("roleName", String.class);
String roleCode = firstRow.get("roleCode", String.class);
```

### Permission Level Mapping
```java
UUID permissionId = tuple.get("permissionId", UUID.class);
String permissionName = tuple.get("permissionName", String.class);
String resource = tuple.get("resource", String.class);
String subResource = tuple.get("subResource", String.class);
String action = tuple.get("action", String.class);
String description = tuple.get("permissionDescription", String.class);
```

### Owner/Delegate Classification
```java
String userType = orgRows.get(0).get("userType", String.class);
if ("owner".equals(userType)) {
    ownerOrg = org;
} else {
    delegateOrgs.add(org);
}
```

## Benefits of Alias-Based Approach

1. **Type Safety**: Direct type casting with `tuple.get("alias", Type.class)`
2. **Maintainability**: Field names are self-documenting
3. **Refactoring Safety**: IDE can track alias usage
4. **Debugging**: Easier to identify which field is being accessed
5. **Code Readability**: Clear mapping between query fields and code

## Error Handling

If an alias doesn't exist or has wrong type, JPA will throw:
- `IllegalArgumentException` for non-existent aliases
- `ClassCastException` for wrong type casting

## Performance

Using `Tuple` with aliases has minimal performance overhead compared to `Object[]` with indices, but provides significant maintainability benefits.
