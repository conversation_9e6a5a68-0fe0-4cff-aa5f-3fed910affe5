package com.smaile.health.repository;

import com.smaile.health.domain.User;
import com.smaile.health.domain.projection.UserProjection;
import com.smaile.health.domain.projection.UserContextProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface UserRepository extends JpaRepository<User, UUID>, JpaSpecificationExecutor<User> {

    User findOneByKeycloakId(String keycloakId);

    @Query("""
            SELECT u.id as id,
                   u.keycloakId as keycloakId,
                   u.status as status,
                   ur.role.id as roleId,
                   uo.organization.id as organizationId,
                   uo.organization.name as organizationName,
                   p.id as permissionId,
                   p.name as permissionName
                FROM User u
                INNER JOIN UserOrganization uo ON u.id = uo.user.id
                INNER JOIN UserRole ur ON ur.userOrganization.id = uo.id
                INNER JOIN ur.role.permissions p
                WHERE u.keycloakId = :keycloakId
            """)
    List<UserContextProjection> findByKeycloakId(String keycloakId);

    Optional<User> findOneByEmail(String email);

    @Query("""
            SELECT u
            FROM User u
                INNER JOIN UserOrganization uo ON u.id = uo.user.id
            WHERE uo.status = :status AND uo.organization.id = COALESCE(:orgId, uo.organization.id)
            """)
    Page<User> findByOrgIdAndStatus(@Param("orgId") UUID orgId, @Param("status") String status, Pageable pageable);

}
