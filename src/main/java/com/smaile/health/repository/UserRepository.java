package com.smaile.health.repository;

import com.smaile.health.domain.User;
import com.smaile.health.domain.projection.UserProjection;
import com.smaile.health.domain.projection.UserOrganizationRolesProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface UserRepository extends JpaRepository<User, UUID>, JpaSpecificationExecutor<User> {

    User findOneByKeycloakId(String keycloakId);

    @Query("""
        SELECT u.id as userId,
               u.keycloakId as keycloakId,
               u.status as userStatus,
               ur.role.id as roleId,
               uo.organization.id as organizationId,
               uo.organization.name as organizationName,
               uo.organization.type as organizationType,
               uo.organization.status as organizationStatus,
               ur.role.code as roleCode,
               ur.role.name as roleName
            FROM User u
            INNER JOIN UserOrganization uo ON u.id = uo.user.id
            INNER JOIN UserRole ur ON ur.userOrganization.id = uo.id
            WHERE u.keycloakId = :keycloakId
        """)
    List<UserOrganizationRolesProjection> findByKeycloakId(String keycloakId);

    Optional<User> findOneByEmail(String email);

    @Query("""
            SELECT u
            FROM User u
                INNER JOIN UserOrganization uo ON u.id = uo.user.id
            WHERE uo.status = :status AND uo.organization.id = COALESCE(:orgId, uo.organization.id)
            """)
    Page<User> findByOrgIdAndStatus(@Param("orgId") UUID orgId, @Param("status") String status, Pageable pageable);

    @Query("""
            SELECT u
            FROM User u
                INNER JOIN UserOrganization uo ON u.id = uo.user.id
            WHERE uo.status = :activationStatus
                AND uo.organization.id = COALESCE(:scopedOrgId, uo.organization.id)
                AND u.id IN :userIdList
            """)
    List<User> findByOrgIdAndIdInAndStatus(UUID scopedOrgId, List<UUID> userIdList, String activationStatus);

}
