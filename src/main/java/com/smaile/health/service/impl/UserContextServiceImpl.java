package com.smaile.health.service.impl;

import com.smaile.health.domain.projection.UserProjection;
import com.smaile.health.repository.UserRepository;
import com.smaile.health.service.UserContextService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class UserContextServiceImpl implements UserContextService {

    private final UserRepository userRepository;

    @Override
    public UserProjection getUserContext(String keycloakId) {
        log.debug("Getting user context for keycloakId: {}", keycloakId);
        
        UserProjection userProjection = userRepository.findUserProjectionByKeycloakId(keycloakId);
        
        if (userProjection == null) {
            log.warn("No user found with keycloakId: {}", keycloakId);
            return null;
        }
        
        log.debug("Found user context for keycloakId: {} with {} delegate organizations", 
                keycloakId, 
                userProjection.getDelegateOrganizations() != null ? userProjection.getDelegateOrganizations().size() : 0);
        
        return userProjection;
    }
}
