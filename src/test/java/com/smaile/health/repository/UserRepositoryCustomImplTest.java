package com.smaile.health.repository;

import com.smaile.health.domain.projection.UserProjection;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
class UserRepositoryCustomImplTest {

    @Autowired
    private UserRepository userRepository;

    @Test
    void testFindUserProjectionByKeycloakId_CompilationTest() {
        // This test verifies that the implementation compiles correctly
        // and the method signature matches the interface
        
        String testKeycloakId = "test-keycloak-id";
        
        // This should compile without errors
        UserProjection result = userRepository.findUserProjectionByKeycloakId(testKeycloakId);
        
        // Result might be null if no user exists, which is fine for compilation test
        if (result != null) {
            // Verify all methods are implemented and return correct types
            assertNotNull(result.getId()); // UUID
            assertNotNull(result.getKeycloakId()); // String
            assertNotNull(result.getEmail()); // String
            assertNotNull(result.getStatus()); // String
            // getOwnerOrganization() and getDelegateOrganizations() can be null
        }
    }
}
